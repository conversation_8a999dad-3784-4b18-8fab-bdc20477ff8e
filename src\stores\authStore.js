// src/stores/authStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态管理
  const user = ref(null) // 当前登录用户
  const users = ref([]) // 所有用户列表
  const isLoading = ref(false) // 加载状态
  const error = ref('') // 错误信息
  const sessionKey = 'bubbleApp_currentUser'
  const usersKey = 'bubbleApp_users'

  // 计算属性：检查是否登录
  const isLoggedIn = computed(() => !!user.value)

  // 计算属性：用户统计信息
  const userStats = computed(() => ({
    totalUsers: users.value.length,
    currentUser: user.value,
    isLoggedIn: isLoggedIn.value,
  }))

  // 初始化：加载用户数据和检查会话
  const initAuth = () => {
    loadUsers()
    checkExistingSession()
    ensureDemoAccount()
  }

  // 从本地存储加载用户数据
  const loadUsers = () => {
    try {
      const usersData = localStorage.getItem(usersKey)
      users.value = usersData ? JSON.parse(usersData) : []
    } catch (err) {
      console.error('加载用户数据失败:', err)
      users.value = []
    }
  }

  // 保存用户数据到本地存储
  const saveUsers = () => {
    try {
      localStorage.setItem(usersKey, JSON.stringify(users.value))
    } catch (err) {
      console.error('保存用户数据失败:', err)
      throw new Error('保存用户数据失败')
    }
  }

  // 检查现有会话（含过期检查）
  const checkExistingSession = () => {
    try {
      const sessionData = localStorage.getItem(sessionKey)
      if (sessionData) {
        const userData = JSON.parse(sessionData)
        // 检查会话是否在24小时内有效
        const now = new Date().getTime()
        if (userData.loginTime && now - userData.loginTime < 24 * 60 * 60 * 1000) {
          user.value = userData
          return true
        } else {
          // 会话过期，清除数据
          logout()
        }
      }
    } catch (err) {
      console.error('检查会话失败:', err)
    }
    return false
  }

  // 注册功能
  const register = async (userData) => {
    isLoading.value = true
    error.value = ''

    try {
      // 验证注册数据
      const validation = validateRegistrationData(userData)
      if (!validation.isValid) {
        throw new Error(validation.message)
      }

      const { username, email, password } = userData

      // 检查用户名是否已存在
      if (users.value.find((u) => u.username === username.trim())) {
        throw new Error('用户名已存在')
      }

      // 检查邮箱是否已存在
      if (users.value.find((u) => u.email === email.trim().toLowerCase())) {
        throw new Error('邮箱已被注册')
      }

      // 创建新用户
      const newUser = {
        id: generateUserId(),
        username: username.trim(),
        email: email.trim().toLowerCase(),
        password: hashPassword(password),
        organization: userData.organization || '',
        createdAt: new Date().toISOString(),
        lastLogin: null,
      }

      users.value.push(newUser)
      saveUsers()
      return { success: true, message: '注册成功，请登录' }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 登录功能
  const login = async (identifier, password, rememberMe = false) => {
    isLoading.value = true
    error.value = ''
    try {
      // 查找用户（支持用户名/邮箱登录）
      const foundUser = users.value.find(
        (u) => u.username === identifier.trim() || u.email === identifier.trim().toLowerCase(),
      )

      if (!foundUser) {
        throw new Error('用户不存在')
      }

      // 验证密码
      if (!verifyPassword(password, foundUser.password)) {
        throw new Error('密码错误')
      }

      // 更新最后登录时间
      foundUser.lastLogin = new Date().toISOString()
      saveUsers()

      // 构建会话用户信息
      const sessionUser = {
        id: foundUser.id,
        username: foundUser.username,
        email: foundUser.email,
        loginTime: new Date().getTime(),
      }

      user.value = sessionUser

      // 根据记住登录选项存储
      if (rememberMe) {
        localStorage.setItem(sessionKey, JSON.stringify(sessionUser))
      } else {
        sessionStorage.setItem(sessionKey, JSON.stringify(sessionUser))
      }

      return true
    } catch (err) {
      error.value = err.message
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 退出登录
  const logout = () => {
    user.value = null
    localStorage.removeItem(sessionKey)
    sessionStorage.removeItem(sessionKey)
  }

  // 验证注册数据
  const validateRegistrationData = (data) => {
    const { username, email, password, confirmPassword } = data

    // 用户名验证
    if (!username || username.trim().length < 3) {
      return { isValid: false, message: '用户名至少需要3个字符', type: 'username' }
    }
    if (username.trim().length > 20) {
      return { isValid: false, message: '用户名不能超过20个字符', type: 'username' }
    }
    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username.trim())) {
      return { isValid: false, message: '用户名只能包含字母、数字、下划线和中文', type: 'username' }
    }

    // 邮箱验证
    if (!email || !isValidEmail(email)) {
      return { isValid: false, message: '请输入有效的邮箱地址' }
    }

    // 密码验证
    if (!password || password.length < 6) {
      return { isValid: false, message: '密码至少需要6个字符' }
    }
    if (password.length > 50) {
      return { isValid: false, message: '密码不能超过50个字符' }
    }
    if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
      return { isValid: false, message: '密码必须包含至少一个字母和一个数字' }
    }

    // 确认密码验证
    if (password !== confirmPassword) {
      return { isValid: false, message: '两次输入的密码不一致' }
    }

    return { isValid: true }
  }

  // 邮箱格式验证
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 密码哈希（实际项目建议使用bcrypt等安全算法）
  const hashPassword = (password) => {
    return btoa(password + 'bubbleApp_salt_2024') // 简单加盐哈希
  }

  // 密码验证
  const verifyPassword = (inputPassword, hashedPassword) => {
    return hashPassword(inputPassword) === hashedPassword
  }

  // 生成用户ID
  const generateUserId = () => {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 确保演示账户存在
  const ensureDemoAccount = () => {
    try {
      const demoExists = users.value.find((u) => u.username === 'demo')
      if (!demoExists) {
        console.log('创建演示账户...')
        const demoUser = {
          id: generateUserId(),
          username: 'demo',
          email: '<EMAIL>',
          password: hashPassword('demo123'),
          organization: '演示用户',
          createdAt: new Date().toISOString(),
          lastLogin: null,
        }
        users.value.push(demoUser)
        saveUsers()
      }
    } catch (err) {
      console.error('创建演示账户失败:', err)
    }
  }

  // 密码强度检查
  const checkPasswordStrength = (password) => {
    let strength = 0
    const feedback = []

    if (password.length >= 8) {
      strength++
    } else {
      feedback.push('至少8个字符')
    }
    if (/[a-z]/.test(password)) strength++
    else feedback.push('包含小写字母')
    if (/[A-Z]/.test(password)) strength++
    else feedback.push('包含大写字母')
    if (/\d/.test(password)) strength++
    else feedback.push('包含数字')
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++
    else feedback.push('包含特殊字符')

    const levels = ['很弱', '弱', '一般', '强', '很强']
    return {
      score: strength,
      level: levels[Math.min(strength, 4)],
      feedback,
    }
  }

  // 清除所有用户数据（开发用）
  const clearAllData = () => {
    if (confirm('确定要清除所有用户数据吗？此操作不可恢复！')) {
      localStorage.removeItem(usersKey)
      localStorage.removeItem(sessionKey)
      sessionStorage.removeItem(sessionKey)
      users.value = []
      user.value = null
      return true
    }
    return false
  }

  return {
    // 状态
    user,
    users,
    isLoading,
    error,

    // 计算属性
    isLoggedIn,
    userStats,

    // 方法
    initAuth,
    login,
    logout,
    register,
    validateRegistrationData,
    checkPasswordStrength,
    clearAllData,
    ensureDemoAccount,
  }
})
