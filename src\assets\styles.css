/* 全局样式重置和基础设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f0f2f5;
  min-height: 100vh;
}

.tool-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #f8f8f8;
  font-size: 13px;
}

.zoom-group {
  min-width: 200px;
}

.slider {
  width: 100px;
  margin: 0 8px;
}

.number-input {
  width: 40px;
  padding: 2px 4px;
  text-align: center;
}

/* OCR识别区域 */
.ocr-controls {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  gap: 10px;
}

.ocr-option {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.upload-container {
  padding: 12px;
}

.ocr-start-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 600;
}

.ocr-start-btn:hover {
  background: #1565c0;
}

.ocr-icon {
  font-size: 18px;
}

.preview-viewport {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  cursor: grab;
}

.preview-image {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  transform-origin: center center;
  display: block;
  cursor: inherit;
  /* 移除max-width和max-height限制，让transform完全控制尺寸 */
}

/* 缩放控制按钮 */
.zoom-controls {
  position: absolute;
  top: 60px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.zoom-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-btn:active {
  transform: scale(0.95);
}

.zoom-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

/* PDF导航控件 */
.pdf-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
  z-index: 10;
}

.pdf-nav-btn {
  width: 32px;
  height: 32px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pdf-nav-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: scale(1.05);
}

.pdf-nav-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.page-info {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 60px;
  text-align: center;
}

.ocr-results {
  margin: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.results-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
}

.chart-section {
  margin: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  background: #f5f5f5;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  font-size: 14px;
  margin: 0;
}

.chart-options {
  padding: 8px 12px;
  background: #f9f9f9;
  display: flex;
  gap: 15px;
}

#bubbleChart {
  border: 1px solid #e0e0e0;
  background: #fcfcfc;
  max-height: 300px;
  border-radius: 4px;
}

.chart-legend {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.legend-items {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
/* 上传区域 */
.upload-area {
  border: 2px dashed #999;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.upload-area:hover {
  border-color: #1976d2;
  background: #e3f2fd;
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #666;
}

.upload-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}

.upload-hint {
  color: #666;
  font-size: 13px;
}

#fileInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .main-container {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    flex: none;
  }

  .toolbar {
    overflow-x: auto;
  }
}

/* 维持原有的动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 维持原有的结果样式 */
.dimension-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 8px;
  background: #f5f5f5;
  margin-bottom: 4px;
  border-radius: 3px;
  border-left: 3px solid #1976d2;
  animation: fadeIn 0.3s ease;
}
