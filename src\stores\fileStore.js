// src/stores/fileStore.js
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useFileStore = defineStore('file', () => {
  // 状态
  const selectedFile = ref(null)
  const ocrStatus = ref('等待文件上传') // 对应原index.html的ocrStatus
  const progress = ref(0) // 对应原progressFill的进度
  const annotations = ref([]) // 标注列表数据

  // 处理文件上传（对应原app.js的文件处理逻辑）
  const handleFileUpload = (file) => {
    selectedFile.value = file
    ocrStatus.value = '正在处理...'
    // 模拟OCR进度（实际项目替换为Tesseract.js逻辑）
    const timer = setInterval(() => {
      progress.value += 10
      if (progress.value >= 100) {
        progress.value = 100
        ocrStatus.value = '处理完成'
        clearInterval(timer)
      }
    }, 500)
  }

  // 添加标注（对应原annotationsList的动态生成）
  const addAnnotation = (annotation) => {
    annotations.value.push({
      id: `anno-${Date.now()}`,
      ...annotation,
    })
  }

  return {
    selectedFile,
    ocrStatus,
    progress,
    annotations,
    handleFileUpload,
    addAnnotation,
  }
})
