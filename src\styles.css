/* 全局样式重置和基础设置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f0f2f5;
  min-height: 100vh;
}

.container {
  width: 100%;
  margin: 0;
  padding: 0;
  background: #ffffff;
  border-radius: 0;
  box-shadow: none;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
  padding: 8px 12px;
  gap: 12px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 8px;
  white-space: nowrap;
}

.toolbar-group > *,
.toolbar-group .export-dropdown {
  align-self: center;
  vertical-align: baseline;
}

.toolbar-group:not(:last-child)::after {
  content: '';
  height: 24px;
  width: 1px;
  background: #ccc;
  margin-left: 8px;
}

.tool-btn {
  padding: 4px 12px;
  background: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 13px;
  min-width: 80px;
  height: 28px;
  line-height: 18px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tool-btn:hover {
  background: #e6e6e6;
  border-color: #ccc;
}

/* 导出下拉菜单样式 */
.export-dropdown {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.export-btn {
  background: #4caf50;
  color: white;
  border: 1px solid #45a049;
  padding: 4px 12px;
  font-size: 13px;
  border-radius: 3px;
  cursor: pointer;
  min-width: 80px;
  height: 28px;
  line-height: 18px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
  margin: 0;
}

.export-btn:hover {
  background: #45a049;
  border-color: #3d8b40;
}

.export-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 180px;
  margin-top: 2px;
}

.export-menu.show {
  display: block;
}

.export-option {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  text-decoration: none;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.export-option:last-child {
  border-bottom: none;
}

.export-option:hover {
  background-color: #f8f9fa;
}

.export-icon {
  margin-right: 10px;
  font-size: 16px;
}

.export-text {
  font-size: 14px;
}

.tool-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #f8f8f8;
  font-size: 13px;
}

.zoom-group {
  min-width: 200px;
}

.slider {
  width: 100px;
  margin: 0 8px;
}

.number-input {
  width: 40px;
  padding: 2px 4px;
  text-align: center;
}

.user-info-container {
  margin-left: auto;
}

/* 主内容区样式 */
.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel,
.right-panel {
  overflow: auto;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #ddd;
}

.left-panel {
  flex: 4;
  background: #fcfcfc;
}

.right-panel {
  flex: 1;
  background: #f9f9f9;
}

.panel-header {
  background: #e9e9e9;
  padding: 8px 12px;
  border-bottom: 1px solid #ddd;
}

.panel-header h2 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* OCR识别区域 */
.ocr-controls {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  gap: 10px;
}

.ocr-option {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.upload-container {
  padding: 12px;
}

.ocr-start-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 600;
}

.ocr-start-btn:hover {
  background: #1565c0;
}

.ocr-icon {
  font-size: 18px;
}

/* 图片预览区域样式 */
.image-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
  height: 450px;
  min-height: 450px;
  max-height: 450px;
}

.preview-header {
  background: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
}

.preview-header h3 {
  margin: 0;
  font-size: 14px;
  color: #495057;
}

.preview-content {
  flex: 1;
  position: relative;
  width: 100%;
  height: 400px;
  min-height: 400px;
  max-height: 400px;
  overflow: hidden;
  background: #f8f9fa;
}

.preview-content:active {
  cursor: grabbing;
}

.preview-viewport {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  cursor: grab;
}

.no-image {
  color: #6c757d;
  font-size: 14px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.preview-image {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  transform-origin: center center;
  display: block;
  cursor: inherit;
  /* 移除max-width和max-height限制，让transform完全控制尺寸 */
}

/* 缩放控制按钮 */
.zoom-controls {
  position: absolute;
  top: 60px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.zoom-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-btn:active {
  transform: scale(0.95);
}

.zoom-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
}

/* PDF导航控件 */
.pdf-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
  z-index: 10;
}

.pdf-nav-btn {
  width: 32px;
  height: 32px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pdf-nav-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: scale(1.05);
}

.pdf-nav-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.page-info {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 60px;
  text-align: center;
}

.ocr-results {
  margin: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.results-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
}

.progress-container {
  margin: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.status-label {
  font-size: 13px;
  color: #495057;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 属性编辑区域 */
.dimension-preview {
  margin: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.dimension-preview h3 {
  background: #f5f5f5;
  padding: 8px 12px;
  margin: 0;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
}

.dimension-preview small {
  font-weight: normal;
  color: #666;
  font-size: 12px;
}

.preview-area {
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fcfcfc;
  padding: 15px;
  color: #999;
}

.no-selection {
  font-style: italic;
  text-align: center;
}

.property-editor {
  padding: 12px;
}

.property-group {
  margin-bottom: 12px;
}

.property-group label {
  display: block;
  margin-bottom: 4px;
  color: #666;
  font-size: 13px;
}

.property-group input,
.property-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.action-btn {
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
}

.apply-btn {
  background: #4caf50;
  color: white;
}

.apply-btn:hover {
  background: #43a047;
}

.delete-btn {
  background: #f44336;
  color: white;
}

.delete-btn:hover {
  background: #e53935;
}

.chart-section {
  margin: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.chart-header {
  background: #f5f5f5;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  font-size: 14px;
  margin: 0;
}

.chart-options {
  padding: 8px 12px;
  background: #f9f9f9;
  display: flex;
  gap: 15px;
}

/* 底部标注列表 */
.annotations-panel {
  border-top: 1px solid #ddd;
}

.annotations-table {
  width: 100%;
  overflow-x: auto;
}

.annotations-table table {
  width: 100%;
  border-collapse: collapse;
}

.annotations-table th,
.annotations-table td {
  padding: 6px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  font-size: 13px;
}

.annotations-table th {
  background: #f5f5f5;
  font-weight: 600;
}

/* 气泡图容器 */
.chart-container {
  padding: 15px;
  display: flex;
  flex-direction: column;
}

#bubbleChart {
  border: 1px solid #e0e0e0;
  background: #fcfcfc;
  max-height: 300px;
  border-radius: 4px;
}

.chart-legend {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.legend-items {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* 保留原有的用户认证相关样式 */
.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-prompt {
  background: white;
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
}

.auth-prompt h2 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.8rem;
}

.auth-prompt p {
  color: #6c757d;
  margin-bottom: 25px;
  line-height: 1.6;
}

.auth-prompt-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.auth-prompt-buttons .btn {
  flex: 1;
  text-decoration: none;
  text-align: center;
}

.auth-prompt-hint {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

/* 用户信息相关样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f8f8f8;
  border: 1px solid #ddd;
}

.user-avatar {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 13px;
}

.user-menu {
  position: relative;
}

.user-menu-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 2px;
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-width: 150px;
  z-index: 1000;
  display: none;
}

.user-menu-dropdown.show {
  display: block;
}

.user-menu-dropdown a {
  display: block;
  padding: 8px 12px;
  color: #333;
  text-decoration: none;
  font-size: 13px;
}

.user-menu-dropdown a:hover {
  background: #f5f5f5;
}

.user-menu-dropdown hr {
  margin: 0;
  border: none;
  border-top: 1px solid #e0e0e0;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #999;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.upload-area:hover {
  border-color: #1976d2;
  background: #e3f2fd;
}

.upload-content {
  pointer-events: none;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #666;
}

.upload-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}

.upload-hint {
  color: #666;
  font-size: 13px;
}

#fileInput {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* 按钮样式 */
.btn {
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  border: none;
  display: inline-block;
  text-align: center;
  text-decoration: none;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
}

.btn-secondary {
  background: #757575;
  color: white;
}

.btn-secondary:hover {
  background: #616161;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  width: 0;
  transition: width 0.3s ease;
}

/* 加载模态框 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 25px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 页脚 */
.footer {
  border-top: 1px solid #ddd;
  padding: 10px;
  text-align: center;
  color: #666;
  font-size: 12px;
  background: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .main-container {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    flex: none;
  }

  .toolbar {
    overflow-x: auto;
  }
}

/* 维持原有的动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 维持原有的结果样式 */
.dimension-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 8px;
  background: #f5f5f5;
  margin-bottom: 4px;
  border-radius: 3px;
  border-left: 3px solid #1976d2;
  animation: fadeIn 0.3s ease;
}
