/* 认证页面专用样式 */

/* 认证页面主体样式 */
.auth-body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  max-height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.auth-container {
  width: 100%;
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
  grid-gap: 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* 个人资料页面特殊布局 */
.auth-container.profile-layout {
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
}

/* 认证头部 */
.auth-header {
  grid-column: 1 / -1;
  text-align: center;
  padding: 8px 15px 6px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.back-link {
  position: absolute;
  top: 20px;
  left: 20px;
  color: #6c757d;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #3498db;
}

.auth-title {
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 2px;
  font-weight: 700;
}

.auth-subtitle {
  color: #6c757d;
  font-size: 0.8rem;
  margin: 0;
}

/* 表单容器 */
.auth-form-container {
  padding: 8px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: hidden;
  max-height: calc(100vh - 120px);
}

.auth-form {
  max-width: 450px;
  width: 100%;
  margin: 0;
}

/* 表单组 */
.form-group {
  margin-bottom: 10px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.label-text {
  font-size: 0.85rem;
}

.required {
  color: #e74c3c;
  margin-left: 3px;
}

/* 表单输入 */
.form-input,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background: #fff;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 下拉选择框特殊样式 */
.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-select option {
  padding: 8px 12px;
  background: white;
  color: #333;
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.form-input.success {
  border-color: #27ae60;
  background: #f2fdf2;
}

/* 密码输入容器 */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #6c757d;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #3498db;
}

/* 输入反馈 */
.input-feedback {
  margin-top: 3px;
  font-size: 0.75rem;
  min-height: 16px;
}

.input-feedback.error {
  color: #e74c3c;
}

.input-feedback.success {
  color: #27ae60;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 5px;
}

.strength-bar {
  width: 100%;
  height: 3px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 3px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.strength-0 {
  width: 0%;
  background: #e9ecef;
}
.strength-fill.strength-1 {
  background: #e74c3c;
}
.strength-fill.strength-2 {
  background: #f39c12;
}
.strength-fill.strength-3 {
  background: #f1c40f;
}
.strength-fill.strength-4 {
  background: #2ecc71;
}
.strength-fill.strength-5 {
  background: #27ae60;
}

.strength-text {
  font-size: 0.7rem;
  color: #6c757d;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.8rem;
  color: #495057;
  gap: 8px;
}

.checkbox-label input[type='checkbox'] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type='checkbox']:checked + .checkmark {
  background: #3498db;
  border-color: #3498db;
}

.checkbox-label input[type='checkbox']:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  flex: 1;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.forgot-password-link {
  color: #3498db;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* 认证按钮 */
.auth-button {
  width: 100%;
  padding: 10px 16px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

/* 消息样式 */
.error-message,
.success-message {
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 10px;
  font-size: 0.8rem;
  text-align: center;
}

.error-message {
  background: #fdf2f2;
  color: #e74c3c;
  border: 1px solid #fadbd8;
}

.success-message {
  background: #f2fdf2;
  color: #27ae60;
  border: 1px solid #d5f4e6;
}

/* 认证页脚 */
.auth-footer {
  text-align: center;
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.auth-link-text {
  color: #6c757d;
  margin: 0;
  font-size: 0.8rem;
}

.auth-link {
  color: #3498db;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

/* 快速登录 */
.quick-login {
  margin-top: 15px;
  text-align: center;
}

.divider {
  position: relative;
  margin: 12px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
}

.divider-text {
  background: white;
  padding: 0 15px;
  color: #6c757d;
  font-size: 0.9rem;
}

.demo-login-button {
  width: 100%;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.demo-login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 15px rgba(243, 156, 18, 0.3);
}

.demo-icon {
  font-size: 1.2rem;
}

.demo-hint {
  margin-top: 6px;
  font-size: 0.75rem;
  color: #6c757d;
}

/* 功能特色 */
.auth-features {
  display: none;
}

.auth-features h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.1rem;
  width: 100%;
  max-width: 350px;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 12px;
  max-width: 350px;
  width: 100%;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text h4 {
  color: #2c3e50;
  margin: 0 0 3px 0;
  font-size: 0.9rem;
}

.feature-text p {
  color: #6c757d;
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.3;
}

/* 系统状态 */
.system-status {
  grid-column: 1 / -1;
  padding: 8px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.system-status h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  text-align: center;
  font-size: 14px;
}

.status-items {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #495057;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #28a745;
  box-shadow: 0 0 6px rgba(40, 167, 69, 0.5);
}

.status-indicator.offline {
  background: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    margin: 0;
    grid-gap: 0;
  }

  .auth-header {
    padding: 15px;
  }

  .auth-title {
    font-size: 1.4rem;
  }

  .auth-form-container {
    padding: 15px;
  }

  .auth-features {
    display: none; /* 在移动端隐藏功能介绍，节省空间 */
  }

  .system-status {
    padding: 8px 15px;
  }

  .form-group {
    margin-bottom: 8px;
  }

  .form-input,
  .form-select {
    padding: 6px 10px;
    font-size: 0.85rem;
  }

  .auth-button {
    padding: 8px 14px;
    font-size: 0.85rem;
  }

  .input-feedback {
    margin-top: 2px;
    font-size: 0.7rem;
    min-height: 14px;
  }

  .password-strength {
    margin-top: 3px;
  }

  .strength-bar {
    height: 2px;
    margin-bottom: 2px;
  }

  .strength-text {
    font-size: 0.65rem;
  }

  .form-options {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .status-items {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .back-link {
    position: static;
    display: block;
    text-align: left;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .auth-body {
    padding: 0;
  }

  .auth-container {
    margin: 0;
    border-radius: 0;
  }

  .auth-header {
    padding: 15px;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .auth-subtitle {
    font-size: 0.9rem;
  }

  .auth-form-container {
    padding: 15px;
  }

  .system-status {
    padding: 8px 15px;
  }
}

/* 个人资料页面样式 */
.profile-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.profile-basic-info h2 {
  margin: 0 0 3px 0;
  font-size: 18px;
  font-weight: 600;
}

.profile-basic-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.profile-main-content {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 允许flex子项收缩 */
}

.profile-left-column {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.profile-right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.profile-section {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  flex-shrink: 0;
}

.section-title {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.info-item span {
  font-size: 16px;
  color: #495057;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.stat-card {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 3px;
}

.stat-label {
  font-size: 13px;
  color: #6c757d;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}

.btn-danger:hover {
  background: #c82333;
  border-color: #bd2130;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #495057;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #495057;
}

.modal-form {
  padding: 25px;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 个人资料页面响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }

  .profile-main-content {
    flex-direction: column;
    gap: 15px;
  }

  .profile-left-column,
  .profile-right-column {
    flex: none;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    padding: 15px;
  }

  .profile-section {
    border-radius: 8px;
    padding: 15px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modal-content {
    width: 100%;
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-form {
    padding: 20px;
  }
}
