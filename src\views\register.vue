<template>
  <div class="auth-body">
    <div class="auth-container">
      <!-- 头部 -->
      <div class="auth-header">
        <router-link to="/" class="back-link">← 返回主页</router-link>
        <h1 class="auth-title">用户注册</h1>
        <p class="auth-subtitle">创建您的账户，开始使用智能尺寸识别功能</p>
      </div>

      <!-- 注册表单 -->
      <div class="auth-form-container">
        <form @submit.prevent="handleRegister" class="auth-form">
          <!-- 用户名字段 -->
          <div class="form-group">
            <label for="username" class="form-label">
              <span class="label-text">用户名</span>
              <span class="required">*</span>
            </label>
            <input
              type="text"
              id="username"
              v-model="formData.username"
              class="form-input"
              placeholder="请输入用户名（3-20个字符）"
              required
              @input="validateField('username')"
            />
            <div class="input-feedback" :class="{ error: errors.username }">
              {{ errors.username }}
            </div>
          </div>

          <!-- 邮箱字段 -->
          <div class="form-group">
            <label for="email" class="form-label">
              <span class="label-text">邮箱地址</span>
              <span class="required">*</span>
            </label>
            <input
              type="email"
              id="email"
              v-model="formData.email"
              class="form-input"
              placeholder="请输入邮箱地址"
              required
              @input="validateField('email')"
            />
            <div class="input-feedback" :class="{ error: errors.email }">
              {{ errors.email }}
            </div>
          </div>

          <!-- 所属机构字段 -->
          <div class="form-group">
            <label for="organization" class="form-label">
              <span class="label-text">所属机构</span>
              <span class="required">*</span>
            </label>
            <select
              id="organization"
              v-model="formData.organization"
              class="form-select"
              required
              @change="validateField('organization')"
            >
              <option value="">请选择所属机构</option>
              <option value="高等院校">高等院校</option>
              <option value="科研院所">科研院所</option>
              <option value="企业单位">企业单位</option>
              <option value="政府部门">政府部门</option>
              <option value="个人用户">个人用户</option>
            </select>
            <div class="input-feedback" :class="{ error: errors.organization }">
              {{ errors.organization }}
            </div>
          </div>

          <!-- 密码字段 -->
          <div class="form-group">
            <label for="password" class="form-label">
              <span class="label-text">密码</span>
              <span class="required">*</span>
            </label>
            <div class="password-input-container">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="password"
                v-model="formData.password"
                class="form-input"
                placeholder="请输入密码（至少6个字符）"
                required
                @input="handlePasswordInput"
              />
              <button type="button" class="password-toggle" @click="showPassword = !showPassword">
                {{ showPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            <!-- 密码强度 -->
            <div class="password-strength">
              <div class="strength-bar">
                <div
                  class="strength-fill"
                  :class="`strength-${passwordStrength.score}`"
                  :style="{ width: `${passwordStrength.score * 20}%` }"
                ></div>
              </div>
              <div class="strength-text">
                密码强度：{{ passwordStrength.level }}
                <span v-if="passwordStrength.feedback.length">
                  （需{{ passwordStrength.feedback.join('、') }}）
                </span>
              </div>
            </div>
            <div class="input-feedback" :class="{ error: errors.password }">
              {{ errors.password }}
            </div>
          </div>

          <!-- 确认密码字段 -->
          <div class="form-group">
            <label for="confirmPassword" class="form-label">
              <span class="label-text">确认密码</span>
              <span class="required">*</span>
            </label>
            <div class="password-input-container">
              <input
                :type="showConfirmPassword ? 'text' : 'password'"
                id="confirmPassword"
                v-model="formData.confirmPassword"
                class="form-input"
                placeholder="请再次输入密码"
                required
                @input="validateField('confirmPassword')"
              />
              <button
                type="button"
                class="password-toggle"
                @click="showConfirmPassword = !showConfirmPassword"
              >
                {{ showConfirmPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            <div class="input-feedback" :class="{ error: errors.confirmPassword }">
              {{ errors.confirmPassword }}
            </div>
          </div>

          <!-- 用户协议 -->
          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                v-model="formData.agreeTerms"
                required
                @change="validateField('agreeTerms')"
              />
              <span class="checkmark"></span>
              <span class="checkbox-text">
                我已阅读并同意 <a href="#" class="link">用户协议</a> 和
                <a href="#" class="link">隐私政策</a>
              </span>
            </label>
            <div class="input-feedback" :class="{ error: errors.agreeTerms }">
              {{ errors.agreeTerms }}
            </div>
          </div>

          <!-- 提交按钮 -->
          <button type="submit" class="auth-button" :disabled="authStore.isLoading">
            <span class="button-text" v-if="!authStore.isLoading">注册账户</span>
            <div class="button-loading" v-else>
              <div class="loading-spinner"></div>
              <span>注册中...</span>
            </div>
          </button>

          <!-- 错误/成功提示 -->
          <div class="error-message" v-if="authStore.error">
            {{ authStore.error }}
          </div>
          <div class="success-message" v-if="successMessage">
            {{ successMessage }}
          </div>
        </form>

        <!-- 登录链接 -->
        <div class="auth-footer">
          <p class="auth-link-text">
            已有账户？ <router-link to="/login" class="auth-link">立即登录</router-link>
          </p>
        </div>
      </div>

      <!-- 功能特色展示 -->
      <div class="auth-features">
        <h3>为什么选择我们？</h3>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <div class="feature-text">
              <h4>智能OCR识别</h4>
              <p>先进的文字识别技术，准确提取尺寸数据</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📊</div>
            <div class="feature-text">
              <h4>可视化图表</h4>
              <p>直观的气泡图展示，让数据一目了然</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">📁</div>
            <div class="feature-text">
              <h4>多格式支持</h4>
              <p>支持PDF和图片文件，满足不同需求</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔒</div>
            <div class="feature-text">
              <h4>数据安全</h4>
              <p>本地存储，保护您的隐私和数据安全</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'

// 初始化路由和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 添加成功消息状态
const successMessage = ref('')

// 表单数据
const formData = ref({
  username: '',
  email: '',
  organization: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false,
})

// 状态管理
const errors = ref({
  username: '',
  email: '',
  organization: '',
  password: '',
  confirmPassword: '',
  agreeTerms: '',
})

const passwordStrength = ref({ score: 0, level: '', feedback: [] })
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 页面初始化
onMounted(() => {
  authStore.initAuth()
  // 已登录用户直接跳转首页（使用计算属性，不加括号）
  if (authStore.isLoggedIn) {
    router.push('/')
  }
})

// 字段验证
const validateField = (field) => {
  errors.value[field] = ''

  switch (field) {
    case 'username':
      if (!formData.value.username || formData.value.username.trim().length < 3) {
        errors.value.username = '用户名至少需要3个字符'
        return false
      }
      if (formData.value.username.trim().length > 20) {
        errors.value.username = '用户名不能超过20个字符'
        return false
      }
      // 检查用户名是否已存在
      const usernameExists = authStore.users.some(
        (u) => u.username === formData.value.username.trim(),
      )
      if (usernameExists) {
        errors.value.username = '用户名已存在'
        return false
      }
      break

    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!formData.value.email || !emailRegex.test(formData.value.email)) {
        errors.value.email = '请输入有效的邮箱地址'
        return false
      }
      // 检查邮箱是否已存在
      const emailExists = authStore.users.some(
        (u) => u.email === formData.value.email.trim().toLowerCase(),
      )
      if (emailExists) {
        errors.value.email = '邮箱已被注册'
        return false
      }
      break

    case 'organization':
      if (!formData.value.organization) {
        errors.value.organization = '请选择所属机构'
        return false
      }
      break

    case 'password':
      if (!formData.value.password || formData.value.password.length < 6) {
        errors.value.password = '密码至少需要6个字符'
        return false
      }
      break

    case 'confirmPassword':
      if (formData.value.password !== formData.value.confirmPassword) {
        errors.value.confirmPassword = '两次输入的密码不一致'
        return false
      }
      break

    case 'agreeTerms':
      if (!formData.value.agreeTerms) {
        errors.value.agreeTerms = '请同意用户协议'
        return false
      }
      break
  }

  return true
}

// 密码输入处理
const handlePasswordInput = () => {
  validateField('password')
  passwordStrength.value = authStore.checkPasswordStrength(formData.value.password)
  if (formData.value.confirmPassword) {
    validateField('confirmPassword')
  }
}

// 提交表单
const handleRegister = async () => {
  // 清除之前的错误和成功消息
  Object.keys(errors.value).forEach((key) => (errors.value[key] = ''))
  successMessage.value = ''

  // 验证所有字段
  const fieldsToValidate = [
    'username',
    'email',
    'organization',
    'password',
    'confirmPassword',
    'agreeTerms',
  ]
  let isValid = true

  fieldsToValidate.forEach((field) => {
    if (!validateField(field)) {
      isValid = false
    }
  })

  if (!isValid) {
    return
  }

  try {
    const result = await authStore.register(formData.value)
    if (result.success) {
      successMessage.value = result.message
      // 1.5秒后跳转到登录页
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  } catch (err) {
    // 显示注册错误
    errors.value.general = err.message || '注册失败，请稍后重试'
  }
}
</script>

<style>
/* 引入全局样式 */
@import url('@/assets/styles.css');
@import url('@/assets/auth-styles.css');

/* 密码强度样式补充 */
.strength-fill.strength-0 {
  background: #e9ecef;
}
.strength-fill.strength-1 {
  background: #e74c3c;
}
.strength-fill.strength-2 {
  background: #f39c12;
}
.strength-fill.strength-3 {
  background: #f1c40f;
}
.strength-fill.strength-4 {
  background: #2ecc71;
}
.strength-fill.strength-5 {
  background: #27ae60;
}
</style>
